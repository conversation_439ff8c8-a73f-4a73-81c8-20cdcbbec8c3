import React, { useCallback, useRef } from 'react';
import {
  Platform,
  ScrollView,
  TouchableOpacity,
  useWindowDimensions,
  View,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import {
  mobileAPCard,
  mobileOHCard,
  mobileQFCard,
  mobileRPCard,
  ricoWithBg,
  ricoWithBgTablet,
  tabletAPCard,
  tabletOHCard,
  tabletQFCard,
  tabletRPCard,
} from 'features/ecoach/assets';
import { colors, sizes } from 'cube-ui-components/dist/cjs/theme/base';
import { RootStackParamList } from 'types';
import { useTranslation } from 'react-i18next';
import styled from '@emotion/native';
import { H4, H6, H7, H8, Icon } from 'cube-ui-components';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import ECoachScreenHeader from 'features/ecoach/components/ECoachScreenHeader';
import useBoundStore from 'hooks/useBoundStore';
import { useGetAgentProfile } from 'hooks/useGetAgentProfile';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { EcoachModuleType } from 'features/ecoach/constants/moduleTypes';
import { ProductFlowType } from 'features/ecoach/store/ecouchSlice';
import HistoryCallTabs from 'features/ecoach/components/HistoryCallTabs';
import AppointmentNavigationCard from 'features/ecoach/components/cards/AppointmentNavigationCard';
import UltimateRoleplayCard from 'features/ecoach/components/cards/UltimateRoleplayCard';
import { HomeBGImg } from 'features/ecoach/screens/home/<USER>';
import { StatusBar } from 'expo-status-bar';

const HiView = styled(View)<{ isTabletMode: boolean }>(({ isTabletMode }) => ({
  flex: 1,
  width: '100%',
  height: '100%',
  marginTop: isTabletMode ? '26%' : Platform.OS === 'ios' ? '40%' : '75%',
}));

const HiText = styled(H4)(() => ({
  textAlign: 'center',
  color: colors.white,
  marginHorizontal: sizes[9],
}));

const DesText = styled(H7)(() => ({
  textAlign: 'center',
  alignSelf: 'center',
  paddingTop: sizes[2],
  color: colors.white,
  marginHorizontal: '10%',
}));

const SessionView = styled(View)<{ isTabletMode: boolean }>(
  ({ isTabletMode }) => ({
    paddingHorizontal: isTabletMode ? '12%' : sizes[4],
    gap: sizes[4],
    paddingBottom: sizes[4],
    marginBottom: sizes[10],
    width: '100%',
  }),
);

const DividerContainer = styled(View)(() => ({
  marginTop: sizes[12],
  width: '100%',
  height: 1,
  backgroundColor: '#8b9793',
  position: 'relative',
  justifyContent: 'center',
  alignItems: 'center',
}));

const GoDownButton = styled(TouchableOpacity)(() => ({
  position: 'absolute',
  left: '50%',
  transform: [{ translateX: -sizes[4] }],
  width: sizes[8],
  height: sizes[8],
  backgroundColor: colors.fwdDarkGreen[50],
  borderRadius: sizes[4],
  padding: sizes[1],
}));

const NavigationCardsContainer = styled(View)<{ isTabletMode: boolean }>(
  ({ isTabletMode }) => ({
    marginTop: sizes[6],
    paddingHorizontal: isTabletMode ? '10%' : sizes[4],
    gap: sizes[4],
  }),
);

const UltimateRoleplayContainer = styled(View)(() => ({
  width: '100%',
  marginBottom: sizes[6],
}));

const OtherCardsContainer = styled(View)<{ isTabletMode: boolean }>(
  ({ isTabletMode }) => ({
    flexDirection: isTabletMode ? 'row' : 'column',
    gap: sizes[8],
    justifyContent: 'space-between',
  }),
);

const WatchVideo = styled(TouchableOpacity)(() => ({
  marginTop: sizes[6],
  width: '100%',
  alignItems: 'center',
  justifyContent: 'center',
  paddingHorizontal: sizes[4],
}));

const bgImageStyle = (windowHeight: number): any =>
  Platform.OS === 'android'
    ? {
        width: '100%',
        height: windowHeight * 1.4,
      }
    : {
        width: '100%',
        height: windowHeight * 1.2,
      };

const HomePageContent = () => {
  const scrollViewRef = useRef<ScrollView>(null);
  const navigation =
    useNavigation<NativeStackNavigationProp<RootStackParamList>>();
  const { data: agentProfile } = useGetAgentProfile();
  const { quickfireVideoUrl, homepageBackground, moduleAvailability } =
    useBoundStore(state => state.ecoach);
  const { height } = useWindowDimensions();

  const validUrl =
    quickfireVideoUrl &&
    quickfireVideoUrl.length > 0 &&
    quickfireVideoUrl.includes('https://');

  const { t } = useTranslation('ecoach');
  const { isTabletMode } = useLayoutAdoptionCheck();
  const { top } = useSafeAreaInsets();

  const goToQuickFireCall = () => {
    navigation.navigate('SelectPolicy', {
      productFlowType: ProductFlowType.QUICKFIRE,
    });
  };

  const goToSelectPolicy = () => {
    navigation.navigate('SelectPolicy', {
      productFlowType: ProductFlowType.FULL_EXPERIENCE,
    });
  };
  const goToAppointmentSetting = () => {
    navigation.navigate('SelectPolicy', {
      productFlowType: ProductFlowType.APPOINTMENT,
    });
  };

  const goToWatchVideoPage = () => {
    validUrl &&
      navigation.navigate('WatchVideoPage', { url: quickfireVideoUrl });
  };

  const goToObjectionHandling = () => {
    navigation.navigate('SelectPolicy', {
      productFlowType: ProductFlowType.OBJECTION_HANDLING,
    });
  };

  const goBottomBtn = () => {
    scrollViewRef.current?.scrollToEnd({ animated: true });
  };

  const getBgImg = useCallback(() => {
    const defaultImage = isTabletMode ? ricoWithBgTablet : ricoWithBg;
    const backgroundImage = isTabletMode
      ? homepageBackground?.background_image_tablet
      : homepageBackground?.background_image_mobile;
    return backgroundImage ? { uri: backgroundImage } : defaultImage;
  }, [isTabletMode, homepageBackground]);

  console.log(
    'useEcoachConfiguration HomePageContent moduleAvailability',
    moduleAvailability,
  );
  return (
    <HomeBGImg
      source={getBgImg()}
      resizeMode={'cover'}
      imageStyle={
        isTabletMode || Platform.OS === 'android' ? bgImageStyle(height) : {}
      }>
      <StatusBar hidden />
      <ScrollView
        style={{ marginTop: Platform.OS === 'ios' ? top : sizes[4] }}
        ref={scrollViewRef}>
        <ECoachScreenHeader
          route={'AiBot'}
          customBackgroundColor={'transparent'}
          leftChildren={
            <H6 fontWeight="bold" color={colors.fwdOrange[5]}>
              {t('training')}{' '}
              <H6 fontWeight="bold" color={colors.fwdOrange[100]}>
                {t('guru')}
              </H6>
            </H6>
          }
          rightChildren={
            <TouchableOpacity
              onPress={() => {
                navigation.navigate('Main', { screen: 'Home' });
              }}>
              <Icon.Close fill={colors.white} />
            </TouchableOpacity>
          }
          customTitle={' '}
        />
        <HiView isTabletMode={isTabletMode}>
          <HiText fontWeight={'bold'}>
            {t('quickFireHiText', {
              name: agentProfile?.person?.firstName || 'Meggie',
            })}{' '}
            {t('quickFireHiSubText')}
          </HiText>
          <DesText>{t('quickFireDesText')}</DesText>
        </HiView>

        <NavigationCardsContainer isTabletMode={isTabletMode}>
          {/* Ultimate Roleplay - Full Width Card */}
          {moduleAvailability?.[EcoachModuleType.FACE_TO_FACE_MEETING] && (
            <UltimateRoleplayContainer>
              <UltimateRoleplayCard
                title={t('ultimateRoleplay')}
                description={t('ultimateRoleplayDesc')}
                timeTag={t('rpMin')}
                timeDescription={t('rpMinutes')}
                imgSrc={isTabletMode ? tabletRPCard : mobileRPCard}
                onPress={goToSelectPolicy}
              />
            </UltimateRoleplayContainer>
          )}

          {/* Other Cards - Smaller Width */}
          <OtherCardsContainer isTabletMode={isTabletMode}>
            {moduleAvailability?.[EcoachModuleType.PRODUCT_KNOWLEDGE] && (
              <AppointmentNavigationCard
                title={t('productKnowledge')}
                description={t('productKnowledgeDesc')}
                timeTag={t('pkMin')}
                timeDescription={t('pkMinutes')}
                imgSrc={isTabletMode ? tabletQFCard : mobileQFCard}
                bgColor={'rgba(92, 35, 0, 0.85)'}
                onPress={goToQuickFireCall}
              />
            )}
            {moduleAvailability?.[EcoachModuleType.APPOINTMENT_SETTING] && (
              <AppointmentNavigationCard
                title={t('appointmentSetting')}
                description={t('appointmentSettingDesc')}
                timeTag={t('apMin')}
                timeDescription={t('apMinutes')}
                imgSrc={isTabletMode ? tabletAPCard : mobileAPCard}
                bgColor={'rgba(183, 71, 1, 0.85)'}
                onPress={goToAppointmentSetting}
              />
            )}
            {moduleAvailability?.[EcoachModuleType.OBJECTION_HANDLING] && (
              <AppointmentNavigationCard
                title={t('objectionHandling')}
                description={t('objectionHandlingDesc')}
                timeTag={t('ohMin')}
                timeDescription={t('ohMinutes')}
                imgSrc={isTabletMode ? tabletOHCard : mobileOHCard}
                bgColor={'rgba(126, 75, 38, 0.85)'}
                onPress={goToObjectionHandling}
              />
            )}
          </OtherCardsContainer>
        </NavigationCardsContainer>

        {validUrl && (
          <WatchVideo onPress={goToWatchVideoPage}>
            <H8 fontWeight="bold" color={colors.fwdOrange[100]}>
              {t('watchVideo')}
            </H8>
          </WatchVideo>
        )}
        <DividerContainer>
          <GoDownButton onPress={goBottomBtn}>
            <Icon.GoDown
              fill={colors.fwdDarkGreen[20]}
              secondFill={colors.fwdGreyDark[100]}
              size={24}></Icon.GoDown>
          </GoDownButton>
        </DividerContainer>
        <SessionView isTabletMode={isTabletMode}>
          <HistoryCallTabs isHomePage={true} />
        </SessionView>
      </ScrollView>
    </HomeBGImg>
  );
};

export default HomePageContent;
